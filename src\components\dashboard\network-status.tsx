"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Wifi, WifiOff, Activity } from "lucide-react"

const networkData = [
  { time: "00:00", bandwidth: 85, latency: 12 },
  { time: "04:00", bandwidth: 92, latency: 8 },
  { time: "08:00", bandwidth: 78, latency: 15 },
  { time: "12:00", bandwidth: 88, latency: 10 },
  { time: "16:00", bandwidth: 95, latency: 6 },
  { time: "20:00", bandwidth: 89, latency: 9 },
  { time: "24:00", bandwidth: 91, latency: 7 },
]

const networkNodes = [
  { name: "Router 1", status: "online", ip: "***********", uptime: "99.9%" },
  { name: "Router 2", status: "online", ip: "***********", uptime: "99.8%" },
  { name: "Switch 1", status: "offline", ip: "***********0", uptime: "0%" },
  { name: "Access Point 1", status: "online", ip: "************", uptime: "99.5%" },
]

export function NetworkStatus() {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      {/* Bandwidth Chart */}
      <Card className="col-span-1">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
            <Activity className="h-5 w-5 mr-2 text-blue-500" />
            Bandwidth Usage
          </CardTitle>
          <p className="text-sm text-gray-600">24-hour bandwidth monitoring</p>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={networkData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="time" 
                axisLine={false}
                tickLine={false}
                className="text-xs text-gray-600"
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                className="text-xs text-gray-600"
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: "white",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                }}
              />
              <Line 
                type="monotone" 
                dataKey="bandwidth" 
                stroke="#3b82f6" 
                strokeWidth={2}
                dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                name="Bandwidth (%)"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Network Nodes */}
      <Card className="col-span-1">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
            <Wifi className="h-5 w-5 mr-2 text-green-500" />
            Network Nodes
          </CardTitle>
          <p className="text-sm text-gray-600">Real-time device status</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {networkNodes.map((node, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  {node.status === "online" ? (
                    <Wifi className="h-4 w-4 text-green-500" />
                  ) : (
                    <WifiOff className="h-4 w-4 text-red-500" />
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900">{node.name}</p>
                    <p className="text-xs text-gray-500">{node.ip}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-xs font-medium ${
                    node.status === "online" ? "text-green-600" : "text-red-600"
                  }`}>
                    {node.status.toUpperCase()}
                  </p>
                  <p className="text-xs text-gray-500">{node.uptime}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
