# Project Structure - Network Billing System

## 📁 Struktur Direktori

```
network-billing-system/
├── 📄 README.md                    # Dokumentasi utama
├── 📄 FEATURES.md                  # Daftar fitur lengkap
├── 📄 API_DOCUMENTATION.md         # Dokumentasi API
├── 📄 PROJECT_STRUCTURE.md         # File ini
├── 📄 .env.example                 # Template environment variables
├── 📄 .gitignore                   # Git ignore rules
├── 📄 .eslintrc.json              # ESLint configuration
├── 📄 package.json                 # Dependencies dan scripts
├── 📄 next.config.js               # Next.js configuration
├── 📄 tailwind.config.js           # Tailwind CSS configuration
├── 📄 postcss.config.js            # PostCSS configuration
├── 📄 tsconfig.json                # TypeScript configuration
│
└── 📁 src/
    ├── 📁 app/                     # Next.js App Router
    │   ├── 📄 layout.tsx           # Root layout
    │   ├── 📄 page.tsx             # Dashboard utama
    │   ├── 📄 globals.css          # Global styles
    │   │
    │   ├── 📁 customers/           # Halaman manajemen pelanggan
    │   │   └── 📄 page.tsx
    │   │
    │   ├── 📁 payments/            # Halaman pembayaran
    │   │   └── 📄 page.tsx
    │   │
    │   └── 📁 network/             # Halaman monitoring jaringan
    │       └── 📄 page.tsx
    │
    ├── 📁 components/              # Komponen React
    │   ├── 📁 ui/                  # Komponen UI dasar
    │   │   ├── 📄 card.tsx         # Card component
    │   │   └── 📄 button.tsx       # Button component
    │   │
    │   ├── 📁 layout/              # Komponen layout
    │   │   ├── 📄 sidebar.tsx      # Sidebar navigation
    │   │   └── 📄 header.tsx       # Header dengan search
    │   │
    │   └── 📁 dashboard/           # Komponen dashboard
    │       ├── 📄 stats-cards.tsx      # Kartu statistik
    │       ├── 📄 revenue-chart.tsx     # Chart pendapatan
    │       ├── 📄 payment-methods.tsx   # Chart metode pembayaran
    │       ├── 📄 network-status.tsx    # Status jaringan
    │       └── 📄 recent-activities.tsx # Aktivitas terbaru
    │
    └── 📁 lib/                     # Utilities dan helpers
        └── 📄 utils.ts             # Utility functions
```

## 🎯 Komponen Utama

### 1. Layout Components

#### Sidebar (`src/components/layout/sidebar.tsx`)
- **Navigasi utama** dengan menu:
  - Dashboard
  - Network Monitor
  - Customers
  - Payments
  - Billing
  - Analytics
  - Bandwidth
  - Security
  - Notifications
  - Settings
- **User profile** section
- **Active state** highlighting
- **Responsive design**

#### Header (`src/components/layout/header.tsx`)
- **Search bar** global
- **Notification bell** dengan badge
- **Settings button**
- **User profile** dropdown
- **Responsive layout**

### 2. Dashboard Components

#### Stats Cards (`src/components/dashboard/stats-cards.tsx`)
- **Total Revenue** dengan trend indicator
- **Active Users** dengan growth metrics
- **Network Status** dengan uptime percentage
- **Failed Payments** dengan alert count
- **Hover effects** dan animations

#### Revenue Chart (`src/components/dashboard/revenue-chart.tsx`)
- **Bar chart** untuk revenue vs payments
- **Monthly data** visualization
- **Interactive tooltips**
- **Responsive design**
- **Custom styling**

#### Payment Methods (`src/components/dashboard/payment-methods.tsx`)
- **Pie chart** distribusi metode pembayaran
- **Color-coded** untuk setiap provider:
  - GoPay (hijau)
  - OVO (ungu)
  - DANA (biru)
  - ShopeePay (orange)
  - Bank Transfer (abu-abu)
- **Legend** dengan persentase

#### Network Status (`src/components/dashboard/network-status.tsx`)
- **Bandwidth chart** 24 jam
- **Device status** list
- **Real-time indicators**
- **Performance metrics**

#### Recent Activities (`src/components/dashboard/recent-activities.tsx`)
- **Activity feed** dengan timestamps
- **Icon-based** categorization
- **Color-coded** status
- **"View all"** link

### 3. Page Components

#### Dashboard (`src/app/page.tsx`)
- **Welcome section** dengan greeting
- **Stats overview** cards
- **Charts section** (revenue + payment methods)
- **Network monitoring** widgets
- **Recent activities** + Quick actions
- **Responsive grid** layout

#### Customers (`src/app/customers/page.tsx`)
- **Customer table** dengan sorting
- **Search dan filter** functionality
- **Status indicators** (online/offline)
- **Package information**
- **Action buttons** untuk management

#### Payments (`src/app/payments/page.tsx`)
- **Payment statistics** cards
- **Transaction table** dengan status
- **Method-based** filtering
- **Export functionality**
- **Status color coding**

#### Network (`src/app/network/page.tsx`)
- **Device monitoring** dashboard
- **Bandwidth charts** (area chart)
- **Connected users** list
- **Real-time status** indicators
- **Performance metrics**

## 🎨 Design System

### Color Palette
- **Primary**: Purple (#8b5cf6)
- **Secondary**: Blue (#3b82f6)
- **Success**: Green (#10b981)
- **Warning**: Yellow (#f59e0b)
- **Error**: Red (#ef4444)
- **Gray Scale**: Various shades

### Typography
- **Font**: Inter (Google Fonts)
- **Headings**: Bold weights
- **Body**: Regular weights
- **Small text**: Light weights

### Components
- **Cards**: Rounded corners, subtle shadows
- **Buttons**: Multiple variants (primary, secondary, ghost)
- **Icons**: Lucide React icon set
- **Charts**: Recharts library dengan custom styling

## 🔧 Technical Stack

### Frontend
- **Next.js 14**: React framework dengan App Router
- **TypeScript**: Type safety
- **Tailwind CSS**: Utility-first CSS
- **Recharts**: Chart library
- **Lucide React**: Icon library

### Development Tools
- **ESLint**: Code linting
- **PostCSS**: CSS processing
- **Autoprefixer**: CSS vendor prefixes

## 📱 Responsive Breakpoints

```css
/* Mobile */
@media (max-width: 767px) { ... }

/* Tablet */
@media (min-width: 768px) and (max-width: 1023px) { ... }

/* Desktop */
@media (min-width: 1024px) { ... }

/* Large Desktop */
@media (min-width: 1280px) { ... }
```

## 🚀 Getting Started

1. **Install Node.js** (18+)
2. **Clone project**
3. **Install dependencies**: `npm install`
4. **Run development**: `npm run dev`
5. **Open browser**: `http://localhost:3000`

## 📦 Build & Deploy

```bash
# Build for production
npm run build

# Start production server
npm start

# Deploy to Vercel
vercel
```

## 🔮 Future Enhancements

### Planned Features
- **Real-time WebSocket** connections
- **Database integration** (Supabase/PostgreSQL)
- **Authentication system** (NextAuth.js)
- **Payment gateway** integration (Midtrans)
- **Email notifications**
- **Mobile app** (React Native)
- **Advanced analytics**
- **Multi-tenant** support

### Additional Pages
- **Billing management**
- **Reports & analytics**
- **User management**
- **System settings**
- **Security logs**
- **Backup & restore**

## 📋 Development Guidelines

### Code Style
- **TypeScript** untuk semua file
- **Functional components** dengan hooks
- **Tailwind classes** untuk styling
- **Consistent naming** conventions

### Component Structure
```typescript
// Component template
"use client"

import { ... } from "..."

interface ComponentProps {
  // Props definition
}

export function ComponentName({ ...props }: ComponentProps) {
  // Component logic
  
  return (
    // JSX structure
  )
}
```

### File Naming
- **PascalCase** untuk components
- **kebab-case** untuk files
- **camelCase** untuk functions
- **UPPER_CASE** untuk constants
