# Network Billing System

Sistem monitoring jaringan dan pembayaran digital yang mirip dengan PHPNuxBill, dibangun dengan teknologi modern.

## 🚀 Fitur Utama

- **Dashboard Analytics** - Statistik real-time dan visualisasi data
- **Monitoring Jaringan** - Status koneksi, bandwidth, dan perangkat
- **Sistem Pembayaran** - Integrasi dengan GoPay, OVO, DANA, ShopeePay
- **Manajemen Pelanggan** - CRUD pelanggan dan paket berlangganan
- **<PERSON><PERSON><PERSON>** - Tracking revenue dan analisis pembayaran
- **Notifikasi Real-time** - Alert sistem dan aktivitas terbaru

## 🛠️ Teknologi

- **Frontend**: Next.js 14 + React + TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui
- **Charts**: Recharts
- **Icons**: Lucide React
- **Database**: PostgreSQL (recommended with Supabase)
- **Payment Gateway**: Midtrans (untuk integrasi payment)

## 📦 Instalasi

### Prasyarat
- Node.js 18+ 
- npm atau yarn

### Langkah Instalasi

1. **Clone atau download project ini**

2. **Install dependencies**
   ```bash
   npm install
   # atau
   yarn install
   ```

3. **Jalankan development server**
   ```bash
   npm run dev
   # atau
   yarn dev
   ```

4. **Buka browser**
   ```
   http://localhost:3000
   ```

## 📁 Struktur Project

```
src/
├── app/                    # Next.js App Router
│   ├── page.tsx           # Dashboard utama
│   ├── customers/         # Halaman pelanggan
│   ├── payments/          # Halaman pembayaran
│   ├── layout.tsx         # Layout utama
│   └── globals.css        # Global styles
├── components/
│   ├── ui/                # Komponen UI dasar
│   ├── layout/            # Komponen layout (sidebar, header)
│   └── dashboard/         # Komponen dashboard
└── lib/
    └── utils.ts           # Utility functions
```

## 🎨 Komponen Dashboard

### Stats Cards
- Total Revenue
- Active Users  
- Network Status
- Failed Payments

### Charts & Analytics
- Revenue Overview (Bar Chart)
- Payment Methods Distribution (Pie Chart)
- Bandwidth Usage (Line Chart)
- Network Nodes Status

### Management
- Customer List dengan status dan paket
- Payment Transactions dengan berbagai metode
- Recent Activities feed
- Quick Actions panel

## 🔧 Kustomisasi

### Menambah Payment Method Baru
Edit file `src/components/dashboard/payment-methods.tsx`:

```typescript
const data = [
  { name: "GoPay", value: 35, color: "#00AA13" },
  { name: "OVO", value: 25, color: "#4C3494" },
  // Tambahkan method baru di sini
]
```

### Menambah Menu Sidebar
Edit file `src/components/layout/sidebar.tsx`:

```typescript
const navigation = [
  {
    name: "Menu Baru",
    href: "/menu-baru", 
    icon: IconName,
  },
  // ...
]
```

### Mengubah Warna Theme
Edit file `src/app/globals.css` untuk mengubah CSS variables:

```css
:root {
  --primary: 262.1 83.3% 57.8%; /* Purple theme */
  /* Ubah nilai ini untuk theme berbeda */
}
```

## 🔌 Integrasi Database

Untuk menghubungkan dengan database, disarankan menggunakan:

1. **Supabase** (PostgreSQL cloud)
2. **Prisma** sebagai ORM
3. **NextAuth.js** untuk authentication

### Setup Supabase
```bash
npm install @supabase/supabase-js
```

Buat file `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 💳 Integrasi Payment Gateway

### Midtrans Setup
```bash
npm install midtrans-client
```

Konfigurasi di `.env.local`:
```
MIDTRANS_SERVER_KEY=your_server_key
MIDTRANS_CLIENT_KEY=your_client_key
MIDTRANS_IS_PRODUCTION=false
```

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm install -g vercel
vercel
```

### Manual Build
```bash
npm run build
npm start
```

## 📱 Responsive Design

Dashboard sudah responsive dan dapat diakses dari:
- Desktop (1024px+)
- Tablet (768px - 1023px)  
- Mobile (320px - 767px)

## 🤝 Kontribusi

1. Fork project ini
2. Buat feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Support

Jika ada pertanyaan atau butuh bantuan:
- Buat issue di GitHub
- Email: <EMAIL>

---

**NetBill** - Modern Network Billing System 🚀
