"use client"

import { Sidebar } from "@/components/layout/sidebar"
import { Header } from "@/components/layout/header"
import { StatsCards } from "@/components/dashboard/stats-cards"
import { RevenueChart } from "@/components/dashboard/revenue-chart"
import { PaymentMethods } from "@/components/dashboard/payment-methods"
import { NetworkStatus } from "@/components/dashboard/network-status"
import { RecentActivities } from "@/components/dashboard/recent-activities"

export default function Dashboard() {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header />
        
        {/* Dashboard Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <div className="container mx-auto px-6 py-8">
            {/* Welcome Section */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome back, Admin! 👋
              </h1>
              <p className="text-gray-600 mt-2">
                Here's what's happening with your network and billing system today.
              </p>
            </div>

            {/* Stats Cards */}
            <div className="mb-8">
              <StatsCards />
            </div>

            {/* Charts Section */}
            <div className="grid gap-6 mb-8 md:grid-cols-6">
              <RevenueChart />
              <PaymentMethods />
            </div>

            {/* Network Status */}
            <div className="mb-8">
              <NetworkStatus />
            </div>

            {/* Recent Activities */}
            <div className="grid gap-6 md:grid-cols-6">
              <RecentActivities />
              
              {/* Quick Actions */}
              <div className="col-span-2">
                <div className="bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg p-6 text-white">
                  <h3 className="text-lg font-semibold mb-2">Quick Actions</h3>
                  <p className="text-purple-100 mb-4 text-sm">
                    Manage your network and billing efficiently
                  </p>
                  <div className="space-y-2">
                    <button className="w-full bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 text-left transition-colors">
                      <div className="font-medium">Add New Customer</div>
                      <div className="text-xs text-purple-100">Register new network user</div>
                    </button>
                    <button className="w-full bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 text-left transition-colors">
                      <div className="font-medium">Generate Invoice</div>
                      <div className="text-xs text-purple-100">Create billing invoice</div>
                    </button>
                    <button className="w-full bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 text-left transition-colors">
                      <div className="font-medium">Network Diagnostics</div>
                      <div className="text-xs text-purple-100">Run system health check</div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
