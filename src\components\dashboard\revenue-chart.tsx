"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { BarC<PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"

const data = [
  { name: "<PERSON>", revenue: 4000, payments: 2400 },
  { name: "Feb", revenue: 3000, payments: 1398 },
  { name: "Mar", revenue: 2000, payments: 9800 },
  { name: "Apr", revenue: 2780, payments: 3908 },
  { name: "May", revenue: 1890, payments: 4800 },
  { name: "<PERSON>", revenue: 2390, payments: 3800 },
  { name: "Jul", revenue: 3490, payments: 4300 },
]

export function RevenueChart() {
  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">
          Revenue Overview
        </CardTitle>
        <p className="text-sm text-gray-600">
          Monthly revenue and payment trends
        </p>
      </CardHeader>
      <CardContent className="pl-2">
        <ResponsiveContainer width="100%" height={350}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="name" 
              axisLine={false}
              tickLine={false}
              className="text-xs text-gray-600"
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              className="text-xs text-gray-600"
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: "white",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
              }}
            />
            <Bar 
              dataKey="revenue" 
              fill="#8b5cf6" 
              radius={[4, 4, 0, 0]}
              name="Revenue (K)"
            />
            <Bar 
              dataKey="payments" 
              fill="#06b6d4" 
              radius={[4, 4, 0, 0]}
              name="Payments (K)"
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
