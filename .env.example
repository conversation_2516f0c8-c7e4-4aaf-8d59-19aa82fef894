# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/netbill"

# Supabase Configuration (Alternative to PostgreSQL)
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Authentication
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Payment Gateway - Midtrans
MIDTRANS_SERVER_KEY="your-midtrans-server-key"
MIDTRANS_CLIENT_KEY="your-midtrans-client-key"
MIDTRANS_IS_PRODUCTION="false"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# SMS Configuration (for notifications)
SMS_API_KEY="your-sms-api-key"
SMS_SENDER_ID="NETBILL"

# Network Monitoring
SNMP_COMMUNITY="public"
NETWORK_RANGE="***********/24"

# Redis (for caching and real-time features)
REDIS_URL="redis://localhost:6379"

# Application Settings
APP_NAME="NetBill"
APP_URL="http://localhost:3000"
ADMIN_EMAIL="<EMAIL>"

# File Upload
UPLOAD_MAX_SIZE="10485760" # 10MB in bytes
ALLOWED_FILE_TYPES="jpg,jpeg,png,pdf,doc,docx"

# Logging
LOG_LEVEL="info"
LOG_FILE_PATH="./logs/app.log"

# Rate Limiting
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW="900000" # 15 minutes in milliseconds

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *" # Daily at 2 AM
BACKUP_RETENTION_DAYS="30"
BACKUP_STORAGE_PATH="./backups"

# Monitoring & Analytics
ANALYTICS_API_KEY="your-analytics-key"
MONITORING_WEBHOOK_URL="https://your-monitoring-service.com/webhook"

# Development Settings
NODE_ENV="development"
DEBUG="true"
ENABLE_MOCK_DATA="true"
