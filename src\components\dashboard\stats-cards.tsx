"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { TrendingUp, TrendingDown, Users, Wifi, CreditCard, AlertTriangle } from "lucide-react"

const stats = [
  {
    title: "Total Revenue",
    value: "Rp 45,231,000",
    change: "+20.1%",
    changeType: "increase",
    icon: CreditCard,
    color: "text-green-600",
    bgColor: "bg-green-50",
  },
  {
    title: "Active Users",
    value: "2,350",
    change: "+180",
    changeType: "increase",
    icon: Users,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
  {
    title: "Network Status",
    value: "99.9%",
    change: "+0.1%",
    changeType: "increase",
    icon: Wifi,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
  },
  {
    title: "Failed Payments",
    value: "23",
    change: "-12",
    changeType: "decrease",
    icon: AlertTriangle,
    color: "text-red-600",
    bgColor: "bg-red-50",
  },
]

export function StatsCards() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
            <div className="flex items-center text-xs text-gray-600 mt-1">
              {stat.changeType === "increase" ? (
                <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
              )}
              <span
                className={
                  stat.changeType === "increase" ? "text-green-600" : "text-red-600"
                }
              >
                {stat.change}
              </span>
              <span className="ml-1">from last month</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
