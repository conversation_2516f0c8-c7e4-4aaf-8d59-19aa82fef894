"use client"

import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search, Plus, Filter, MoreHorizontal, Wifi, WifiOff } from "lucide-react"

const customers = [
  {
    id: "CUST001",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+62 812-3456-7890",
    package: "Premium 100Mbps",
    status: "active",
    lastPayment: "2024-01-15",
    nextBilling: "2024-02-15",
    totalPaid: "Rp 1,500,000",
  },
  {
    id: "CUST002", 
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+62 813-4567-8901",
    package: "Standard 50Mbps",
    status: "active",
    lastPayment: "2024-01-10",
    nextBilling: "2024-02-10",
    totalPaid: "Rp 900,000",
  },
  {
    id: "CUST003",
    name: "<PERSON>",
    email: "<EMAIL>", 
    phone: "+62 814-5678-9012",
    package: "Basic 25Mbps",
    status: "suspended",
    lastPayment: "2023-12-15",
    nextBilling: "2024-01-15",
    totalPaid: "Rp 450,000",
  },
  {
    id: "CUST004",
    name: "Lisa Chen",
    email: "<EMAIL>",
    phone: "+62 815-6789-0123",
    package: "Premium 100Mbps",
    status: "active",
    lastPayment: "2024-01-20",
    nextBilling: "2024-02-20",
    totalPaid: "Rp 2,100,000",
  },
]

export default function CustomersPage() {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <div className="container mx-auto px-6 py-8">
            {/* Page Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">Customers</h1>
              <p className="text-gray-600 mt-2">
                Manage your network customers and their subscriptions
              </p>
            </div>

            {/* Actions Bar */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search customers..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filter
                </Button>
                <Button className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700">
                  <Plus className="h-4 w-4" />
                  Add Customer
                </Button>
              </div>
            </div>

            {/* Customers Table */}
            <Card>
              <CardHeader>
                <CardTitle>Customer List</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-600">Customer</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">Package</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">Last Payment</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">Next Billing</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">Total Paid</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {customers.map((customer) => (
                        <tr key={customer.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4">
                            <div>
                              <div className="font-medium text-gray-900">{customer.name}</div>
                              <div className="text-sm text-gray-500">{customer.email}</div>
                              <div className="text-sm text-gray-500">{customer.phone}</div>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <span className="text-sm text-gray-900">{customer.package}</span>
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex items-center gap-2">
                              {customer.status === "active" ? (
                                <Wifi className="h-4 w-4 text-green-500" />
                              ) : (
                                <WifiOff className="h-4 w-4 text-red-500" />
                              )}
                              <span className={`text-sm font-medium ${
                                customer.status === "active" ? "text-green-600" : "text-red-600"
                              }`}>
                                {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                              </span>
                            </div>
                          </td>
                          <td className="py-4 px-4 text-sm text-gray-900">{customer.lastPayment}</td>
                          <td className="py-4 px-4 text-sm text-gray-900">{customer.nextBilling}</td>
                          <td className="py-4 px-4 text-sm font-medium text-gray-900">{customer.totalPaid}</td>
                          <td className="py-4 px-4">
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
