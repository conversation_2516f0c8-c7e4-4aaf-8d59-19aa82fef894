# API Documentation - Network Billing System

## Base URL
```
http://localhost:3000/api
```

## Authentication
All API endpoints require authentication using JWT tokens.

```javascript
Headers: {
  "Authorization": "Bearer <your-jwt-token>",
  "Content-Type": "application/json"
}
```

## Endpoints

### 🏠 Dashboard

#### GET /api/dashboard/stats
Get dashboard statistics

**Response:**
```json
{
  "totalRevenue": "45231000",
  "activeUsers": 2350,
  "networkStatus": "99.9",
  "failedPayments": 23,
  "revenueChange": "+20.1%",
  "usersChange": "+180"
}
```

#### GET /api/dashboard/revenue
Get revenue chart data

**Response:**
```json
{
  "data": [
    {
      "month": "Jan",
      "revenue": 4000000,
      "payments": 2400000
    }
  ]
}
```

### 👥 Customers

#### GET /api/customers
Get all customers with pagination

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search term
- `status` (string): Filter by status (active, suspended, inactive)

**Response:**
```json
{
  "customers": [
    {
      "id": "CUST001",
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+62 812-3456-7890",
      "package": "Premium 100Mbps",
      "status": "active",
      "lastPayment": "2024-01-15",
      "nextBilling": "2024-02-15",
      "totalPaid": "1500000"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

#### POST /api/customers
Create new customer

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+62 812-3456-7890",
  "address": "Jl. Sudirman No. 123",
  "packageId": "PKG001",
  "startDate": "2024-01-01"
}
```

#### PUT /api/customers/:id
Update customer

#### DELETE /api/customers/:id
Delete customer

#### POST /api/customers/:id/suspend
Suspend customer account

#### POST /api/customers/:id/activate
Activate customer account

### 💳 Payments

#### GET /api/payments
Get all payments

**Query Parameters:**
- `page`, `limit`, `search` (same as customers)
- `status` (string): completed, pending, failed
- `method` (string): gopay, ovo, dana, shopeepay, bank_transfer
- `dateFrom`, `dateTo` (string): Date range filter

**Response:**
```json
{
  "payments": [
    {
      "id": "PAY001",
      "customerId": "CUST001",
      "customerName": "John Doe",
      "amount": 150000,
      "method": "gopay",
      "status": "completed",
      "transactionId": "TXN123456",
      "createdAt": "2024-01-15T14:30:00Z",
      "completedAt": "2024-01-15T14:31:00Z",
      "invoiceId": "INV-2024-001"
    }
  ]
}
```

#### POST /api/payments
Create payment

**Request Body:**
```json
{
  "customerId": "CUST001",
  "amount": 150000,
  "method": "gopay",
  "description": "Monthly subscription fee"
}
```

#### GET /api/payments/:id
Get payment details

#### POST /api/payments/:id/confirm
Confirm payment (for manual methods)

#### POST /api/payments/:id/refund
Process refund

### 🌐 Network

#### GET /api/network/devices
Get all network devices

**Response:**
```json
{
  "devices": [
    {
      "id": "DEV001",
      "name": "Main Router",
      "type": "router",
      "ip": "***********",
      "status": "online",
      "uptime": "99.9%",
      "load": "45%",
      "temperature": "42°C",
      "lastSeen": "2024-01-15T15:00:00Z"
    }
  ]
}
```

#### GET /api/network/bandwidth
Get bandwidth usage data

**Query Parameters:**
- `period` (string): 1h, 24h, 7d, 30d
- `deviceId` (string): Filter by device

**Response:**
```json
{
  "data": [
    {
      "timestamp": "2024-01-15T14:00:00Z",
      "upload": 45,
      "download": 78,
      "deviceId": "DEV001"
    }
  ]
}
```

#### GET /api/network/users
Get connected users

**Response:**
```json
{
  "users": [
    {
      "customerId": "CUST001",
      "name": "John Doe",
      "ip": "*************",
      "device": "Laptop",
      "bandwidth": "25 Mbps",
      "duration": "2h 15m",
      "connectedAt": "2024-01-15T12:45:00Z"
    }
  ]
}
```

#### POST /api/network/devices/:id/restart
Restart network device

#### POST /api/network/users/:id/disconnect
Disconnect user

### 📊 Analytics

#### GET /api/analytics/revenue
Get revenue analytics

**Query Parameters:**
- `period` (string): daily, weekly, monthly, yearly
- `dateFrom`, `dateTo` (string): Date range

#### GET /api/analytics/customers
Get customer analytics

#### GET /api/analytics/network
Get network performance analytics

#### GET /api/analytics/payments
Get payment method analytics

### 🔔 Notifications

#### GET /api/notifications
Get user notifications

**Response:**
```json
{
  "notifications": [
    {
      "id": "NOT001",
      "type": "payment",
      "title": "Payment received",
      "message": "Payment of Rp 150,000 received from John Doe",
      "read": false,
      "createdAt": "2024-01-15T14:30:00Z"
    }
  ]
}
```

#### POST /api/notifications/:id/read
Mark notification as read

#### POST /api/notifications/read-all
Mark all notifications as read

### ⚙️ Settings

#### GET /api/settings
Get application settings

#### PUT /api/settings
Update settings

**Request Body:**
```json
{
  "companyName": "NetBill ISP",
  "currency": "IDR",
  "timezone": "Asia/Jakarta",
  "emailNotifications": true,
  "smsNotifications": false,
  "autoSuspendDays": 7,
  "gracePeriodDays": 3
}
```

### 📋 Packages

#### GET /api/packages
Get all internet packages

**Response:**
```json
{
  "packages": [
    {
      "id": "PKG001",
      "name": "Basic 25Mbps",
      "speed": "25 Mbps",
      "price": 50000,
      "description": "Basic internet package",
      "active": true
    }
  ]
}
```

#### POST /api/packages
Create new package

#### PUT /api/packages/:id
Update package

#### DELETE /api/packages/:id
Delete package

### 📄 Invoices

#### GET /api/invoices
Get all invoices

#### POST /api/invoices
Generate invoice

#### GET /api/invoices/:id
Get invoice details

#### GET /api/invoices/:id/pdf
Download invoice PDF

#### POST /api/invoices/:id/send
Send invoice via email

### 🔐 Authentication

#### POST /api/auth/login
User login

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "token": "jwt-token-here",
  "user": {
    "id": "USER001",
    "name": "Admin User",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

#### POST /api/auth/logout
User logout

#### POST /api/auth/refresh
Refresh JWT token

#### POST /api/auth/forgot-password
Request password reset

#### POST /api/auth/reset-password
Reset password

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "email": "Email is required",
      "phone": "Invalid phone number format"
    }
  }
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API requests are limited to 100 requests per 15 minutes per IP address.

## Webhooks

### Payment Webhooks
Receive payment status updates from payment gateways:

```
POST /api/webhooks/payments/midtrans
POST /api/webhooks/payments/gopay
POST /api/webhooks/payments/ovo
```

### Network Webhooks
Receive network device status updates:

```
POST /api/webhooks/network/device-status
POST /api/webhooks/network/bandwidth-alert
```
