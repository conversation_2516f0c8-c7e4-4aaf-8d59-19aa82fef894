"use client"

import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Wifi, WifiOff, Activity, Server, Router, Smartphone, RefreshCw } from "lucide-react"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from "recharts"

const bandwidthData = [
  { time: "00:00", upload: 45, download: 78 },
  { time: "04:00", upload: 52, download: 85 },
  { time: "08:00", upload: 38, download: 92 },
  { time: "12:00", upload: 65, download: 88 },
  { time: "16:00", upload: 72, download: 95 },
  { time: "20:00", upload: 58, download: 89 },
  { time: "24:00", upload: 48, download: 82 },
]

const devices = [
  {
    name: "Main Router",
    type: "router",
    ip: "***********",
    status: "online",
    uptime: "99.9%",
    load: "45%",
    temperature: "42°C",
  },
  {
    name: "Core Switch",
    type: "switch", 
    ip: "***********",
    status: "online",
    uptime: "99.8%",
    load: "67%",
    temperature: "38°C",
  },
  {
    name: "Access Point 1",
    type: "wifi",
    ip: "***********0",
    status: "online",
    uptime: "99.5%",
    load: "23%",
    temperature: "35°C",
  },
  {
    name: "Access Point 2", 
    type: "wifi",
    ip: "***********1",
    status: "offline",
    uptime: "0%",
    load: "0%",
    temperature: "N/A",
  },
  {
    name: "Backup Server",
    type: "server",
    ip: "***********00",
    status: "online",
    uptime: "99.2%",
    load: "89%",
    temperature: "55°C",
  },
]

const connectedUsers = [
  { name: "John Doe", ip: "***********01", device: "Laptop", bandwidth: "25 Mbps", duration: "2h 15m" },
  { name: "Sarah Wilson", ip: "***********02", device: "Smartphone", bandwidth: "15 Mbps", duration: "45m" },
  { name: "Mike Johnson", ip: "***********03", device: "Desktop", bandwidth: "50 Mbps", duration: "4h 30m" },
  { name: "Lisa Chen", ip: "*************", device: "Tablet", bandwidth: "20 Mbps", duration: "1h 20m" },
]

const getDeviceIcon = (type: string) => {
  switch (type) {
    case "router":
      return <Router className="h-5 w-5" />
    case "switch":
      return <Server className="h-5 w-5" />
    case "wifi":
      return <Wifi className="h-5 w-5" />
    case "server":
      return <Server className="h-5 w-5" />
    default:
      return <Activity className="h-5 w-5" />
  }
}

const getStatusColor = (status: string) => {
  return status === "online" ? "text-green-500" : "text-red-500"
}

const getLoadColor = (load: string) => {
  const percentage = parseInt(load)
  if (percentage < 50) return "text-green-600"
  if (percentage < 80) return "text-yellow-600"
  return "text-red-600"
}

export default function NetworkPage() {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <div className="container mx-auto px-6 py-8">
            {/* Page Header */}
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Network Monitor</h1>
                <p className="text-gray-600 mt-2">
                  Real-time network status and device monitoring
                </p>
              </div>
              <Button className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700">
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>

            {/* Network Stats */}
            <div className="grid gap-4 md:grid-cols-4 mb-8">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Devices</p>
                      <p className="text-2xl font-bold text-gray-900">5</p>
                    </div>
                    <div className="p-2 bg-blue-50 rounded-lg">
                      <Server className="h-6 w-6 text-blue-500" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Online</p>
                      <p className="text-2xl font-bold text-gray-900">4</p>
                    </div>
                    <div className="p-2 bg-green-50 rounded-lg">
                      <Wifi className="h-6 w-6 text-green-500" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Offline</p>
                      <p className="text-2xl font-bold text-gray-900">1</p>
                    </div>
                    <div className="p-2 bg-red-50 rounded-lg">
                      <WifiOff className="h-6 w-6 text-red-500" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Connected Users</p>
                      <p className="text-2xl font-bold text-gray-900">4</p>
                    </div>
                    <div className="p-2 bg-purple-50 rounded-lg">
                      <Smartphone className="h-6 w-6 text-purple-500" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Bandwidth Chart */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-900">
                  Bandwidth Usage (24 Hours)
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Upload and download traffic monitoring
                </p>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={bandwidthData}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis 
                      dataKey="time" 
                      axisLine={false}
                      tickLine={false}
                      className="text-xs text-gray-600"
                    />
                    <YAxis 
                      axisLine={false}
                      tickLine={false}
                      className="text-xs text-gray-600"
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: "white",
                        border: "1px solid #e5e7eb",
                        borderRadius: "8px",
                        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                      }}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="download" 
                      stackId="1"
                      stroke="#3b82f6" 
                      fill="#3b82f6"
                      fillOpacity={0.6}
                      name="Download (Mbps)"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="upload" 
                      stackId="1"
                      stroke="#8b5cf6" 
                      fill="#8b5cf6"
                      fillOpacity={0.6}
                      name="Upload (Mbps)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <div className="grid gap-6 md:grid-cols-2">
              {/* Network Devices */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-gray-900">
                    Network Devices
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Status and performance of network equipment
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {devices.map((device, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${device.status === "online" ? "bg-green-50" : "bg-red-50"}`}>
                            <div className={getStatusColor(device.status)}>
                              {getDeviceIcon(device.type)}
                            </div>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">{device.name}</p>
                            <p className="text-xs text-gray-500">{device.ip}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`text-xs font-medium ${getStatusColor(device.status)}`}>
                            {device.status.toUpperCase()}
                          </p>
                          <p className="text-xs text-gray-500">Uptime: {device.uptime}</p>
                          <p className={`text-xs ${getLoadColor(device.load)}`}>Load: {device.load}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Connected Users */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-gray-900">
                    Connected Users
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Currently active network connections
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {connectedUsers.map((user, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-blue-50 rounded-lg">
                            <Smartphone className="h-4 w-4 text-blue-500" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">{user.name}</p>
                            <p className="text-xs text-gray-500">{user.ip} • {user.device}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-xs font-medium text-gray-900">{user.bandwidth}</p>
                          <p className="text-xs text-gray-500">{user.duration}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
