"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { CreditCard, UserPlus, Wifi, AlertTriangle, CheckCircle } from "lucide-react"

const activities = [
  {
    id: 1,
    type: "payment",
    title: "Payment received from <PERSON>",
    description: "GoPay payment of Rp 150,000",
    time: "2 minutes ago",
    icon: CreditCard,
    iconColor: "text-green-500",
    iconBg: "bg-green-50",
  },
  {
    id: 2,
    type: "user",
    title: "New customer registered",
    description: "<PERSON> joined the network",
    time: "15 minutes ago",
    icon: UserPlus,
    iconColor: "text-blue-500",
    iconBg: "bg-blue-50",
  },
  {
    id: 3,
    type: "network",
    title: "Network maintenance completed",
    description: "Router 2 firmware updated successfully",
    time: "1 hour ago",
    icon: CheckCircle,
    iconColor: "text-green-500",
    iconBg: "bg-green-50",
  },
  {
    id: 4,
    type: "alert",
    title: "High bandwidth usage detected",
    description: "Customer ID #1234 exceeded 90% quota",
    time: "2 hours ago",
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    iconColor: "text-yellow-500",
    iconBg: "bg-yellow-50",
  },
  {
    id: 5,
    type: "network",
    title: "Connection restored",
    description: "Switch 1 back online after maintenance",
    time: "3 hours ago",
    icon: Wifi,
    iconColor: "text-green-500",
    iconBg: "bg-green-50",
  },
]

export function RecentActivities() {
  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">
          Recent Activities
        </CardTitle>
        <p className="text-sm text-gray-600">
          Latest system events and notifications
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3">
              <div className={`p-2 rounded-lg ${activity.iconBg} flex-shrink-0`}>
                <activity.icon className={`h-4 w-4 ${activity.iconColor}`} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900">
                  {activity.title}
                </p>
                <p className="text-sm text-gray-600">
                  {activity.description}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {activity.time}
                </p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button className="text-sm text-purple-600 hover:text-purple-700 font-medium">
            View all activities →
          </button>
        </div>
      </CardContent>
    </Card>
  )
}
