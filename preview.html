<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Billing System - Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg">
            <div class="p-6">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="wifi" class="w-5 h-5 text-white"></i>
                    </div>
                    <h1 class="text-xl font-bold text-gray-900">NetBill</h1>
                </div>
            </div>
            
            <nav class="mt-6">
                <div class="px-6 py-3 bg-purple-50 border-r-4 border-purple-600">
                    <div class="flex items-center space-x-3 text-purple-700">
                        <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
                        <span class="font-medium">Dashboard</span>
                    </div>
                </div>
                <div class="px-6 py-3 text-gray-600 hover:bg-gray-50 cursor-pointer">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="users" class="w-5 h-5"></i>
                        <span>Customers</span>
                    </div>
                </div>
                <div class="px-6 py-3 text-gray-600 hover:bg-gray-50 cursor-pointer">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="credit-card" class="w-5 h-5"></i>
                        <span>Payments</span>
                    </div>
                </div>
                <div class="px-6 py-3 text-gray-600 hover:bg-gray-50 cursor-pointer">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="wifi" class="w-5 h-5"></i>
                        <span>Network</span>
                    </div>
                </div>
                <div class="px-6 py-3 text-gray-600 hover:bg-gray-50 cursor-pointer">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="file-text" class="w-5 h-5"></i>
                        <span>Reports</span>
                    </div>
                </div>
                <div class="px-6 py-3 text-gray-600 hover:bg-gray-50 cursor-pointer">
                    <div class="flex items-center space-x-3">
                        <i data-lucide="settings" class="w-5 h-5"></i>
                        <span>Settings</span>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <h2 class="text-lg font-semibold text-gray-900">Dashboard</h2>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <i data-lucide="bell" class="w-6 h-6 text-gray-400"></i>
                            <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                        </div>
                        <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">A</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50">
                <div class="container mx-auto px-6 py-8">
                    <!-- Welcome Section -->
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900">Welcome back, Admin! 👋</h1>
                        <p class="text-gray-600 mt-2">Here's what's happening with your network and billing system today.</p>
                    </div>

                    <!-- Stats Cards -->
                    <div class="grid gap-6 mb-8 md:grid-cols-4">
                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                                    <p class="text-2xl font-bold text-gray-900">Rp 45,231,000</p>
                                    <p class="text-sm text-green-600">+20.1% from last month</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                                    <p class="text-2xl font-bold text-gray-900">2,350</p>
                                    <p class="text-sm text-blue-600">+180 new this month</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Network Status</p>
                                    <p class="text-2xl font-bold text-gray-900">99.2%</p>
                                    <p class="text-sm text-green-600">Excellent uptime</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="wifi" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Failed Payments</p>
                                    <p class="text-2xl font-bold text-gray-900">23</p>
                                    <p class="text-sm text-red-600">-12% from last month</p>
                                </div>
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="alert-circle" class="w-6 h-6 text-red-600"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="grid gap-6 mb-8 md:grid-cols-6">
                        <!-- Revenue Chart -->
                        <div class="col-span-4 bg-white rounded-lg p-6 shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue Overview</h3>
                            <canvas id="revenueChart" width="400" height="200"></canvas>
                        </div>

                        <!-- Payment Methods -->
                        <div class="col-span-2 bg-white rounded-lg p-6 shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Methods</h3>
                            <canvas id="paymentChart" width="200" height="200"></canvas>
                            <div class="mt-4 space-y-2">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">GoPay</span>
                                    </div>
                                    <span class="text-sm font-medium">35%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">OVO</span>
                                    </div>
                                    <span class="text-sm font-medium">25%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">DANA</span>
                                    </div>
                                    <span class="text-sm font-medium">20%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">ShopeePay</span>
                                    </div>
                                    <span class="text-sm font-medium">20%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Network Status & Recent Activities -->
                    <div class="grid gap-6 md:grid-cols-6">
                        <!-- Recent Activities -->
                        <div class="col-span-4 bg-white rounded-lg p-6 shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
                            <div class="space-y-4">
                                <div class="flex items-center space-x-4">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="check" class="w-5 h-5 text-green-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">Payment received from John Doe</p>
                                        <p class="text-xs text-gray-500">2 minutes ago</p>
                                    </div>
                                    <span class="text-sm font-medium text-green-600">+Rp 150,000</span>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="user-plus" class="w-5 h-5 text-blue-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">New customer registered</p>
                                        <p class="text-xs text-gray-500">5 minutes ago</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="wifi" class="w-5 h-5 text-yellow-600"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">Network maintenance completed</p>
                                        <p class="text-xs text-gray-500">1 hour ago</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="col-span-2">
                            <div class="gradient-bg rounded-lg p-6 text-white">
                                <h3 class="text-lg font-semibold mb-2">Quick Actions</h3>
                                <p class="text-purple-100 mb-4 text-sm">Manage your network and billing efficiently</p>
                                <div class="space-y-2">
                                    <button class="w-full bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 text-left transition-colors">
                                        <div class="font-medium">Add New Customer</div>
                                        <div class="text-xs text-purple-100">Register new network user</div>
                                    </button>
                                    <button class="w-full bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 text-left transition-colors">
                                        <div class="font-medium">Generate Invoice</div>
                                        <div class="text-xs text-purple-100">Create billing invoice</div>
                                    </button>
                                    <button class="w-full bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 text-left transition-colors">
                                        <div class="font-medium">Network Diagnostics</div>
                                        <div class="text-xs text-purple-100">Run system health check</div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Revenue (Millions)',
                    data: [12, 19, 15, 25, 22, 30],
                    backgroundColor: 'rgba(147, 51, 234, 0.8)',
                    borderColor: 'rgba(147, 51, 234, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Payment Methods Chart
        const paymentCtx = document.getElementById('paymentChart').getContext('2d');
        new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: ['GoPay', 'OVO', 'DANA', 'ShopeePay'],
                datasets: [{
                    data: [35, 25, 20, 20],
                    backgroundColor: [
                        '#10B981',
                        '#8B5CF6',
                        '#3B82F6',
                        '#F97316'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</body>
</html>
